import { createRouter, createWebHashHistory } from 'vue-router'

// 布局组件
const Layout = () => import('@/layout/index.vue')

// 路由配置
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录', hidden: true }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '首页', icon: 'HomeFilled', affix: true }
      }
    ]
  },
  {
    path: '/topup',
    component: Layout,
    redirect: '/topup',
    meta: { title: '结算', icon: 'Money' },
    children: [
      {
        path: 'jiesuan',
        name: 'jiesuan',
        component: () => import('@/views/topup/jiesuan.vue'),
        meta: { title: '结算', icon: 'Money' }
      }
    ]
  },
  {
    path: '/skuMove',
    component: Layout,
    redirect: '/skuMove',
    meta: { title: 'SKU转移', icon: 'Money' },
    children: [
      {
        path: 'skuMove',
        name: 'sku<PERSON><PERSON>',
        component: () => import('@/views/topup/skuMove.vue'),
        meta: { title: 'SKU转移', icon: 'Money' }
      }
    ]
  },
  {
    path: '/callback',
    component: Layout,
    redirect: '/callback',
    meta: { title: '手动回调', icon: 'Setting' },
    children: [
      {
        path: 'jkCb',
        name: 'jkCb',
        component: () => import('@/views/topup/callback/jkCb.vue'),
        meta: { title: '即刻回调', icon: 'Setting' }
      },
      {
        path: 'ydCb',
        name: 'ydCb',
        component: () => import('@/views/topup/callback/ydCb.vue'),
        meta: { title: '雨端回调', icon: 'Setting' }
      },
      {
        path: 'hpCb',
        name: 'hpCb',
        component: () => import('@/views/topup/callback/hpCb.vue'),
        meta: { title: '哈普回调', icon: 'Setting' }
      },
    ]
  },
  {
    path: '/product',
    component: Layout,
    redirect: '/product/index',
    meta: { title: '产品管理', icon: 'Goods' },
    children: [
      {
        path: 'index',
        name: 'ProductList',
        component: () => import('@/views/product/index.vue'),
        meta: { title: '产品列表', icon: 'List' }
      },
      {
        path: 'create',
        name: 'ProductCreate',
        component: () => import('@/views/product/create.vue'),
        meta: { title: '新增产品', icon: 'Plus', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'ProductEdit',
        component: () => import('@/views/product/edit.vue'),
        meta: { title: '编辑产品', icon: 'Edit', hidden: true }
      }
    ]
  },
  {
    path: '/product',
    component: Layout,
    redirect: '/product/index',
    meta: { title: '产品管理', icon: 'Goods' },
    children: [
      {
        path: 'index',
        name: 'ProductList',
        component: () => import('@/views/product/index.vue'),
        meta: { title: '产品列表', icon: 'List' }
      },
      {
        path: 'create',
        name: 'ProductCreate',
        component: () => import('@/views/product/create.vue'),
        meta: { title: '新增产品', icon: 'Plus', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'ProductEdit',
        component: () => import('@/views/product/edit.vue'),
        meta: { title: '编辑产品', icon: 'Edit', hidden: true }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/user',
    meta: { title: '系统管理', icon: 'Setting', hidden: true },
    children: [
      {
        path: 'user',
        name: 'User',
        component: () => import('@/views/system/user/index.vue'),
        meta: { title: '用户管理', icon: 'User' }
      },
      {
        path: 'role',
        name: 'Role',
        component: () => import('@/views/system/role/index.vue'),
        meta: { title: '角色管理', icon: 'UserFilled' }
      }
    ]
  },
  {
    path: '/example',
    component: Layout,
    redirect: '/example/list',
    meta: { title: '示例页面', icon: 'Document', hidden: true },
    children: [
      {
        path: 'list',
        name: 'List',
        component: () => import('@/views/example/list.vue'),
        meta: { title: '列表页面', icon: 'List' }
      },
      {
        path: 'create',
        name: 'Create',
        component: () => import('@/views/example/create.vue'),
        meta: { title: '新增页面', icon: 'Plus' }
      },
      {
        path: 'edit/:id',
        name: 'Edit',
        component: () => import('@/views/example/edit.vue'),
        meta: { title: '编辑页面', icon: 'Edit', hidden: true }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/error/404.vue'),
    meta: { hidden: true }
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 获取token
  const token = localStorage.getItem('token')

  // 如果访问登录页，直接放行
  if (to.path === '/login') {
    next()
    return
  }

  // 如果没有token，重定向到登录页
  if (!token) {
    next('/login')
    return
  }

  // 有token，放行
  next()
})

export default router
